# QuickShop Backend

A comprehensive backend for a Quick-Commerce platform built with Spring Boot, supporting millions of users with features like OTP-based authentication, product management, cart functionality, order processing, and async report generation.

## Features

### Authentication & Authorization
- OTP-based authentication for email and mobile
- JWT token-based authorization
- Role-based access control (Customer/Retailer)
- Password and OTP login options

### User Management
- Customer and Retailer user types
- Email and mobile verification
- User profile management

### Product Management
- CRUD operations for products (Retailer only)
- Product validation (cost, MRP, stock)
- Category-based organization
- Stock management with inventory tracking

### Shopping Cart
- Add/remove/update items in cart
- Quantity limits (max 10 per product, 20 total)
- Multi-cart support
- Real-time total calculation

### Order Management
- Order placement with inventory deduction
- Order cancellation and returns
- Automatic inventory restoration
- Order history and tracking

### Delivery Simulation
- 2-minute delivery simulation
- Automatic order status updates
- Delivery cancellation support

### Invoice System
- Automatic invoice generation on delivery
- Email delivery of invoices
- Detailed order breakdown

### Reporting (Retailer)
- Async CSV/PDF report generation
- Sales reports by date range
- S3 storage for report files
- Secure download links with expiration

### API Features
- Product search and filtering
- Category-based browsing
- Price range filtering
- Retailer-specific product views
- Pagination and sorting

## Technology Stack

- **Framework**: Spring Boot 3.2.0
- **Database**: H2 (development), PostgreSQL (production)
- **Security**: Spring Security with JWT
- **Caching**: Redis (production), Simple (development)
- **File Storage**: AWS S3
- **Email**: Spring Mail
- **Documentation**: Built-in API endpoints
- **Testing**: JUnit 5

## Quick Start

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- (Optional) PostgreSQL for production
- (Optional) Redis for caching
- (Optional) AWS S3 for file storage

### Running the Application

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd quickshop
   ```

2. **Run with Maven**
   ```bash
   mvn spring-boot:run
   ```

3. **Access the application**
   - API Base URL: `http://localhost:8080/api`
   - H2 Console: `http://localhost:8080/api/h2-console`

### Test Accounts

The application comes with pre-populated test data:

**Retailers:**
- `<EMAIL>` / `password123` (Fresh Mart)
- `<EMAIL>` / `password123` (Tech Store)
- `<EMAIL>` / `password123` (Fashion Hub)

**Customers:**
- `<EMAIL>` / `password123` (John Doe)
- `<EMAIL>` / `password123` (Jane Smith)
- `<EMAIL>` / `password123` (Mike Johnson)

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login with email/mobile and password
- `POST /api/auth/login-otp` - Login with OTP
- `POST /api/auth/send-otp` - Send OTP for verification
- `POST /api/auth/verify-otp` - Verify OTP

### Customer APIs
- `GET /api/customer/products` - Get all products
- `GET /api/customer/products/search?q={term}` - Search products
- `GET /api/customer/products/category/{category}` - Products by category
- `GET /api/customer/products/{id}` - Get product details
- `POST /api/customer/cart/add` - Add item to cart
- `GET /api/customer/cart` - Get active cart
- `POST /api/customer/orders` - Place order
- `GET /api/customer/orders` - Get order history

### Retailer APIs
- `POST /api/retailer/products` - Create product
- `GET /api/retailer/products` - Get retailer's products
- `PUT /api/retailer/products/{id}` - Update product
- `DELETE /api/retailer/products/{id}` - Delete product
- `POST /api/retailer/reports/sales` - Generate sales report
- `GET /api/retailer/reports` - Get reports list

## Configuration

### Environment Variables

For production deployment, set these environment variables:

```bash
# Database
DATABASE_URL=******************************************
DATABASE_USERNAME=quickshop
DATABASE_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret_key

# Email
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# AWS S3
AWS_S3_BUCKET=your-s3-bucket
AWS_REGION=us-east-1
AWS_ACCESS_KEY=your_access_key
AWS_SECRET_KEY=your_secret_key

# Redis (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

## Testing

Run tests with:
```bash
mvn test
```

## Deployment

### Local Development
The application runs with H2 in-memory database by default.

### Production
1. Set up PostgreSQL database
2. Configure Redis for caching
3. Set up AWS S3 bucket for file storage
4. Configure email service
5. Set environment variables
6. Deploy to your preferred platform

## API Documentation

Once the application is running, you can explore the APIs using:
- Built-in endpoints at `/api/`
- H2 Console for database inspection at `/api/h2-console`

## Features Implemented

✅ OTP-based authentication
✅ Role-based access control
✅ Product CRUD with validation
✅ Shopping cart with limits
✅ Order management
✅ Inventory tracking
✅ Delivery simulation
✅ Invoice generation
✅ Async report generation
✅ Email notifications
✅ Search and filtering
✅ Mock data population

## Bonus Features

✅ Multi-cart support
✅ API response caching
🔄 CI/CD Pipeline (ready for setup)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
