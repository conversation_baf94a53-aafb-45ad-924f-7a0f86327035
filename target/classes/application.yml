spring:
  application:
    name: quickshop-backend
  
  profiles:
    active: dev
  
  datasource:
    url: jdbc:h2:mem:quickshop
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  
  cache:
    type: simple
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

server:
  port: 8080
  servlet:
    context-path: /api

logging:
  level:
    com.quickshop: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

app:
  jwt:
    secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
    expiration: 86400000 # 24 hours
  
  otp:
    expiration: 300000 # 5 minutes
  
  aws:
    s3:
      bucket: ${AWS_S3_BUCKET:quickshop-reports}
      region: ${AWS_REGION:us-east-1}
      access-key: ${AWS_ACCESS_KEY:}
      secret-key: ${AWS_SECRET_KEY:}
  
  delivery:
    simulation-time: 120000 # 2 minutes in milliseconds

---
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: ${DATABASE_URL:******************************************}
    username: ${DATABASE_USERNAME:quickshop}
    password: ${DATABASE_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  
  cache:
    type: redis
  
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}

logging:
  level:
    com.quickshop: INFO
    org.springframework.security: WARN
