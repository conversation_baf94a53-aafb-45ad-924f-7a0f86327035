package com.quickshop;

import com.quickshop.repository.UserRepository;
import com.quickshop.repository.ProductRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
@ActiveProfiles("test")
class QuickShopApplicationTests {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private ProductRepository productRepository;

    @Test
    void contextLoads() {
        // Test that the application context loads successfully
    }
    
    @Test
    void testDataInitialization() {
        // Test that mock data is created
        assertTrue(userRepository.count() > 0, "Users should be created");
        assertTrue(productRepository.count() > 0, "Products should be created");
    }
}
