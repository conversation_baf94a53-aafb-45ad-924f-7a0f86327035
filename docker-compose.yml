version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DATABASE_URL=***********************************
      - DATABASE_USERNAME=quickshop
      - DATABASE_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - db
      - redis
    networks:
      - quickshop-network

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=quickshop
      - POSTGRES_USER=quickshop
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - quickshop-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quickshop-network

volumes:
  postgres_data:
  redis_data:

networks:
  quickshop-network:
    driver: bridge
