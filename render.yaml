services:
  - type: web
    name: quickshop-backend
    env: java
    buildCommand: mvn clean package -DskipTests
    startCommand: java -jar target/quickshop-backend-0.0.1-SNAPSHOT.jar
    healthCheckPath: /api/actuator/health
    envVars:
      - key: SPRING_PROFILES_ACTIVE
        value: prod
      - key: DATABASE_URL
        fromDatabase:
          name: quickshop-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: quickshop-redis
          property: connectionString

databases:
  - name: quickshop-db
    databaseName: quickshop
    user: quickshop

services:
  - type: redis
    name: quickshop-redis
    ipAllowList: []
